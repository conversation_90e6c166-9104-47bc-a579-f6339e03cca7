import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

/// Utility class for storing and retrieving verification data in SharedPreferences
class VerificationStorage {
  // Keys for SharedPreferences
  static const String _keyIdType = 'verification_id_type';
  static const String _keyFrontPhotoPath = 'verification_front_photo_path';
  static const String _keyBackPhotoPath = 'verification_back_photo_path';
  static const String _keyBrgyPhotoPath = 'verification_brgy_photo_path';
  static const String _keyConfirmation = 'verification_confirmation';
  static const String _keyUserId = 'verification_user_id';
  static const String _keyFacePhotoPath = 'verification_face_photo_path';
  static const String _keyTimestamp = 'verification_timestamp';

  /// Save ID verification data to SharedPreferences
  static Future<void> saveIdVerificationData({
    required String idType,
    required String frontPhotoPath,
    required String backPhotoPath,
    required String brgyPhotoPath,
    required bool confirmation,
    required int userId,
  }) async {
    final prefs = await SharedPreferences.getInstance();

    await prefs.setString(_keyIdType, idType);
    await prefs.setString(_keyFrontPhotoPath, frontPhotoPath);
    await prefs.setString(_keyBackPhotoPath, backPhotoPath);
    await prefs.setString(_keyBrgyPhotoPath, brgyPhotoPath);
    await prefs.setBool(_keyConfirmation, confirmation);
    await prefs.setInt(_keyUserId, userId);
    await prefs.setString(_keyTimestamp, DateTime.now().toIso8601String());

    print('VerificationStorage: ID verification data saved to SharedPreferences');
  }

  /// Save face photo path to SharedPreferences
  static Future<void> saveFacePhotoPath(String facePhotoPath) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_keyFacePhotoPath, facePhotoPath);
    print('VerificationStorage: Face photo path saved to SharedPreferences');
  }

  /// Get all verification data from SharedPreferences
  static Future<Map<String, dynamic>?> getVerificationData() async {
    final prefs = await SharedPreferences.getInstance();

    final idType = prefs.getString(_keyIdType);
    final frontPhotoPath = prefs.getString(_keyFrontPhotoPath);
    final backPhotoPath = prefs.getString(_keyBackPhotoPath);
    final brgyPhotoPath = prefs.getString(_keyBrgyPhotoPath);
    final confirmation = prefs.getBool(_keyConfirmation);
    final userId = prefs.getInt(_keyUserId);
    final facePhotoPath = prefs.getString(_keyFacePhotoPath);
    final timestamp = prefs.getString(_keyTimestamp);

    // Check if we have the minimum required data
    if (idType == null || frontPhotoPath == null || backPhotoPath == null ||
        brgyPhotoPath == null || confirmation == null || userId == null) {
      print('VerificationStorage: Incomplete verification data in SharedPreferences');
      return null;
    }

    return {
      'idType': idType,
      'frontPhotoPath': frontPhotoPath,
      'backPhotoPath': backPhotoPath,
      'brgyPhotoPath': brgyPhotoPath,
      'confirmation': confirmation,
      'userId': userId,
      'facePhotoPath': facePhotoPath,
      'timestamp': timestamp,
    };
  }

  /// Check if ID verification data exists in SharedPreferences
  static Future<bool> hasIdVerificationData() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.containsKey(_keyIdType) &&
        prefs.containsKey(_keyFrontPhotoPath) &&
        prefs.containsKey(_keyBackPhotoPath) &&
        prefs.containsKey(_keyBrgyPhotoPath);
  }

  /// Check if face photo data exists in SharedPreferences
  static Future<bool> hasFacePhotoData() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.containsKey(_keyFacePhotoPath);
  }

  /// Clear all verification data from SharedPreferences
  static Future<void> clearVerificationData() async {
    final prefs = await SharedPreferences.getInstance();

    await prefs.remove(_keyIdType);
    await prefs.remove(_keyFrontPhotoPath);
    await prefs.remove(_keyBackPhotoPath);
    await prefs.remove(_keyBrgyPhotoPath);
    await prefs.remove(_keyConfirmation);
    await prefs.remove(_keyUserId);
    await prefs.remove(_keyFacePhotoPath);
    await prefs.remove(_keyTimestamp);

    print('VerificationStorage: All verification data cleared from SharedPreferences');
  }

  /// Get only ID verification data (without face photo)
  static Future<Map<String, dynamic>?> getIdVerificationData() async {
    final prefs = await SharedPreferences.getInstance();

    final idType = prefs.getString(_keyIdType);
    final frontPhotoPath = prefs.getString(_keyFrontPhotoPath);
    final backPhotoPath = prefs.getString(_keyBackPhotoPath);
    final brgyPhotoPath = prefs.getString(_keyBrgyPhotoPath);
    final confirmation = prefs.getBool(_keyConfirmation);
    final userId = prefs.getInt(_keyUserId);
    final timestamp = prefs.getString(_keyTimestamp);

    if (idType == null || frontPhotoPath == null || backPhotoPath == null ||
        brgyPhotoPath == null || confirmation == null || userId == null) {
      return null;
    }

    return {
      'idType': idType,
      'frontPhotoPath': frontPhotoPath,
      'backPhotoPath': backPhotoPath,
      'brgyPhotoPath': brgyPhotoPath,
      'confirmation': confirmation,
      'userId': userId,
      'timestamp': timestamp,
    };
  }

  /// Get face photo path from SharedPreferences
  static Future<String?> getFacePhotoPath() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_keyFacePhotoPath);
  }

  /// Check if verification data is expired (older than 24 hours)
  static Future<bool> isVerificationDataExpired() async {
    final prefs = await SharedPreferences.getInstance();
    final timestampStr = prefs.getString(_keyTimestamp);

    if (timestampStr == null) return true;

    try {
      final timestamp = DateTime.parse(timestampStr);
      final now = DateTime.now();
      final difference = now.difference(timestamp);

      // Consider data expired if older than 24 hours
      return difference.inHours > 24;
    } catch (e) {
      print('VerificationStorage: Error parsing timestamp: $e');
      return true;
    }
  }

  /// Clear expired verification data
  static Future<void> clearExpiredData() async {
    if (await isVerificationDataExpired()) {
      await clearVerificationData();
      print('VerificationStorage: Expired verification data cleared');
    }
  }
}
