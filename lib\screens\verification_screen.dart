import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io'; // For File
import 'dart:async'; // NEW: Import for Timer
import 'package:hanapp/utils/constants.dart' as Constants;
import 'package:hanapp/utils/api_config.dart'; // For API base URL
import 'package:hanapp/services/verification_service.dart';
import 'package:hanapp/utils/auth_service.dart'; // For User model and AuthService
import 'package:hanapp/models/user.dart'; // User model
import 'package:hanapp/utils/cache_cleaner.dart'; // For cache cleaning
import 'package:hanapp/screens/enhanced_face_verification_screen.dart';
import 'package:hanapp/screens/verified_badge_payment_screen.dart'; // NEW: Import payment screen
import 'live_photo_capture_screen.dart'; // NEW: Import for face recognition step
import 'package:hanapp/widgets/animated_wiggle_button.dart'; // NEW: Import animated wiggle button
import 'package:hanapp/utils/verification_storage.dart'; // NEW: Import verification storage

class VerificationScreen extends StatefulWidget {
  const VerificationScreen({super.key});

  @override
  State<VerificationScreen> createState() => _VerificationScreenState();
}

class _VerificationScreenState extends State<VerificationScreen> {
  String? _selectedIdType;
  XFile? _idPhotoFront; // Stores the selected front ID image file
  XFile? _idPhotoBack; // Stores the selected back ID image file
  XFile? _brgyClearancePhoto; // NEW: Barangay Clearance photo
  bool? _confirmIdBelongsToUser; // Yes/No radio button state
  bool _isLoading = false; // For submission loading indicator

  // NEW: Store uploaded image URLs from server
  String? _uploadedFrontImageUrl;
  String? _uploadedBackImageUrl;
  String? _uploadedBrgyImageUrl;

  // NEW: Auto-refresh verification status
  Timer? _statusPollingTimer;
  static const Duration _pollingInterval = Duration(seconds: 10); // Check every 10 seconds
  bool _isPollingActive = false;

  User? _currentUser; // Current logged-in user
  String _currentVerificationStatus = 'unverified';
  bool _isBadgeAcquired = false;
  bool _isIdVerified = false; // If ID documents are fully accepted by admin
  bool _isWiggleAnimating = false; // NEW: Control wiggle animation for Get Badge button
  bool _isDialogButtonWiggling = false; // NEW: Control wiggle animation for dialog button
  Timer? _wiggleTimer; // NEW: Timer for periodic wiggle animation
  Timer? _dialogButtonTimer; // NEW: Timer for periodic dialog button wiggle animation

  final VerificationService _verificationService = VerificationService();
  final ImagePicker _picker = ImagePicker();

  final List<String> _idTypes = [
    'Passport',
    'Driver\'s License',
    'National ID',
    'Voter\'s ID',
    'PhilHealth ID',
    'UMID',
    'Postal ID',
    'School ID',
  ];

  @override
  void initState() {
    super.initState();
    _loadUserAndVerificationStatus();
    _cleanupExpiredData();
    _checkPendingVerificationData();
  }

  @override
  void dispose() {
    _stopStatusPolling(); // Stop polling when leaving screen
    _wiggleTimer?.cancel(); // NEW: Cancel timer when disposing
    _dialogButtonTimer?.cancel(); // NEW: Cancel dialog button timer when disposing
    super.dispose();
  }

  Future<void> _loadUserAndVerificationStatus() async {
    setState(() {
      _isLoading = true;
    });
    try {
      print('VerificationScreen: Loading user with API-first approach (no SharedPreferences dependency)...');
      try {
        _currentUser = await AuthService.getUser();
        print('VerificationScreen: User loaded successfully');

        // ===== DETAILED USER INFO LOGGING =====
        if (_currentUser != null) {
          print('========== USER INFO DEBUG ==========');
          print('User ID: ${_currentUser!.id}');
          print('Full Name: ${_currentUser!.fullName}');
          print('Email: ${_currentUser!.email}');
          print('Role: ${_currentUser!.role}');
          print('Profile Picture: ${_currentUser!.profilePictureUrl ?? 'None'}');
          print('Contact Number: ${_currentUser!.contactNumber ?? 'None'}');
          print('Address: ${_currentUser!.addressDetails ?? 'None'}');
          print('Gender: ${_currentUser!.gender ?? 'None'}');
          print('Birthday: ${_currentUser!.birthday ?? 'None'}');
          print('Created At: ${_currentUser!.createdAt ?? 'None'}');
          print('Is Available: ${_currentUser!.isAvailable}');
          print('Average Rating: ${_currentUser!.averageRating ?? 0.0}');
          print('Review Count: ${_currentUser!.reviewCount ?? 0}');
          print('Total Profit: ${_currentUser!.totalProfit ?? 0.0}');
          print('Total Reviews: ${_currentUser!.totalReviews}');
          print('--- VERIFICATION INFO ---');
          print('Is Verified (Email): ${_currentUser!.isVerified}');
          print('ID Verified: ${_currentUser!.isIdVerified}');
          print('Badge Acquired: ${_currentUser!.isBadgeAcquired}');
          print('Verification Status: "${_currentUser!.verificationStatus}" (Type: ${_currentUser!.verificationStatus.runtimeType})');
          print('Badge Status: "${_currentUser!.badgeStatus}" (Type: ${_currentUser!.badgeStatus.runtimeType})');
          print('--- VERIFICATION PHOTOS ---');
          print('ID Photo Front: ${_currentUser!.idPhotoFrontUrl ?? 'None'}');
          print('ID Photo Back: ${_currentUser!.idPhotoBackUrl ?? 'None'}');
          print('Barangay Clearance: ${_currentUser!.brgyClearancePhotoUrl ?? 'None'}');
          print('Live Photo: ${_currentUser!.livePhotoUrl ?? 'None'}');
          print('--- LOCATION INFO ---');
          print('Latitude: ${_currentUser!.latitude ?? 'None'}');
          print('Longitude: ${_currentUser!.longitude ?? 'None'}');
          print('--- ACCOUNT STATUS ---');
          print('Is Deleted: ${_currentUser!.isDeleted}');
          print('Banned Until: ${_currentUser!.bannedUntil ?? 'None'}');
          print('=====================================');
        } else {
          print('VerificationScreen: _currentUser is NULL!');
        }
        // ===== END USER INFO LOGGING =====

      } catch (e) {
        print('VerificationScreen: ERROR loading user: $e');
        print('VerificationScreen: Attempting to refresh user data...');

        // Try to refresh user data from API
        try {
          _currentUser = await AuthService.refreshUserData();
          print('VerificationScreen: User refresh successful');
        } catch (retryError) {
          print('VerificationScreen: User refresh failed: $retryError');

          // Last resort: clear all cache and try once more
          await AuthService.clearUserCache();
          _currentUser = await AuthService.getUser();
          print('VerificationScreen: Final attempt successful after cache clear');
        }
      }

      if (_currentUser == null || _currentUser!.id == null) {
        print('VerificationScreen: User not logged in');
        _showSnackBar('User not logged in. Please log in to verify.', isError: true);
        if (mounted) Navigator.of(context).pushReplacementNamed('/login'); // Redirect to login
        return;
      }

      print('VerificationScreen: User loaded successfully. ID: ${_currentUser!.id}');
      print('VerificationScreen: Current user verification_status from AuthService: ${_currentUser!.verificationStatus}');

      print('VerificationScreen: Calling _verificationService.getVerificationStatus()...');
      try {
        final response = await _verificationService.getVerificationStatus(userId: _currentUser!.id!);
        print('VerificationScreen: getVerificationStatus() completed successfully');

        if (response['success']) {
          final statusData = response['status_data'];

          // ===== DETAILED VERIFICATION STATUS LOGGING =====
          print('======= VERIFICATION STATUS DEBUG =======');
          print('Raw API Response: $response');
          print('Status Data: $statusData');
          print('--- VERIFICATION FIELDS ---');
          print('verification_status: "${statusData['verification_status']}" (Type: ${statusData['verification_status'].runtimeType})');
          print('badge_status: "${statusData['badge_status']}" (Type: ${statusData['badge_status'].runtimeType})');
          print('id_verified: ${statusData['id_verified']} (Type: ${statusData['id_verified'].runtimeType})');
          print('badge_acquired: ${statusData['badge_acquired']} (Type: ${statusData['badge_acquired'].runtimeType})');
          print('--- PHOTO URLS ---');
          print('id_photo_front_url: ${statusData['id_photo_front_url'] ?? 'None'}');
          print('id_photo_back_url: ${statusData['id_photo_back_url'] ?? 'None'}');
          print('brgy_clearance_photo_url: ${statusData['brgy_clearance_photo_url'] ?? 'None'}');
          print('live_photo_url: ${statusData['live_photo_url'] ?? 'None'}');
          print('--- COMPARISON ---');
          print('AuthService verification_status: ${_currentUser!.verificationStatus}');
          print('API verification_status: ${statusData['verification_status']}');
          print('Status match: ${_currentUser!.verificationStatus == statusData['verification_status']}');
          print('=========================================');
          // ===== END VERIFICATION STATUS LOGGING =====

          setState(() {
            // Safely handle potential type casting issues
            _currentVerificationStatus = _safeStringValue(statusData['verification_status'], 'unverified');
            _isIdVerified = statusData['id_verified'] ?? false;
            _isBadgeAcquired = statusData['badge_acquired'] ?? false;

            // Store uploaded image URLs from server
            _uploadedFrontImageUrl = statusData['id_photo_front_url'];
            _uploadedBackImageUrl = statusData['id_photo_back_url'];
            _uploadedBrgyImageUrl = statusData['brgy_clearance_photo_url'];

            print('🖼️ Loaded image URLs: Front=${_uploadedFrontImageUrl}, Back=${_uploadedBackImageUrl}, Brgy=${_uploadedBrgyImageUrl}');
          });

          // NEW: Start polling if in pending state
          if (_currentVerificationStatus == 'pending_id_review' ||
              _currentVerificationStatus == 'pending_face_match') {
            _startStatusPolling();
          }

          // Show badge prompt if ID is verified, badge not acquired, AND verification status is 'verified'
          if (_isIdVerified && !_isBadgeAcquired && _currentVerificationStatus == 'verified') {
            // Start wiggle animation for the Get Badge button
            setState(() {
              _isWiggleAnimating = true;
            });

            // Stop animation after 3 seconds
            Future.delayed(const Duration(seconds: 3), () {
              if (mounted) {
                setState(() {
                  _isWiggleAnimating = false;
                });
              }
            });

            // Start periodic wiggle animation to draw attention
            _startPeriodicWiggleAnimation();

            // Removed automatic modal - modal should only show when user clicks Next button
          } else {
            // Stop periodic animation if user is not eligible
            _stopPeriodicWiggleAnimation();
          }
        } else {
          _showSnackBar('Failed to load verification status: ${response['message']}', isError: true);
          setState(() {
            _currentVerificationStatus = 'unverified'; // Fallback
          });
        }
      } catch (e) {
        print('VerificationScreen: ERROR in getVerificationStatus(): $e');
        _showSnackBar('Error loading verification status: $e', isError: true);
        setState(() {
          _currentVerificationStatus = 'unverified'; // Fallback
        });
      }
    } catch (e, stackTrace) {
      print('VerificationScreen: Error loading user or verification status: $e');
      print('VerificationScreen: Stack trace: $stackTrace');
      _showSnackBar('Error loading verification status: $e', isError: true);
      setState(() {
        _currentVerificationStatus = 'unverified'; // Fallback
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // Helper method to safely convert any value to string
  String _safeStringValue(dynamic value, String defaultValue) {
    if (value == null) return defaultValue;
    if (value is String) return value.isEmpty ? defaultValue : value;
    if (value is int || value is double || value is bool) return value.toString();
    return value.toString();
  }

  // NEW: Start automatic status polling
  void _startStatusPolling() {
    if (_isPollingActive) return; // Already polling

    _isPollingActive = true;
    print('VerificationScreen: Starting automatic status polling every ${_pollingInterval.inSeconds} seconds');

    _statusPollingTimer = Timer.periodic(_pollingInterval, (timer) async {
      if (!mounted) {
        _stopStatusPolling();
        return;
      }

      // Only poll if we're in a pending state
      if (_currentVerificationStatus == 'pending_id_review' ||
          _currentVerificationStatus == 'pending_face_match') {
        print('VerificationScreen: Polling verification status...');
        await _refreshVerificationStatus();
      } else if (_currentVerificationStatus == 'verified') {
        // Stop polling if verified
        print('VerificationScreen: Verification complete, stopping polling');
        _stopStatusPolling();
        _showVerificationCompleteDialog();
      }
    });
  }

  // NEW: Stop automatic status polling
  void _stopStatusPolling() {
    if (_statusPollingTimer != null) {
      _statusPollingTimer!.cancel();
      _statusPollingTimer = null;
      _isPollingActive = false;
      print('VerificationScreen: Stopped automatic status polling');
    }
  }

  // NEW: Refresh verification status without loading indicator
  Future<void> _refreshVerificationStatus() async {
    try {
      final currentUser = await AuthService.getUser();
      if (currentUser == null || currentUser.id == null) return;

      final response = await _verificationService.getVerificationStatus(userId: currentUser.id!);

      if (response['success'] && mounted) {
        final statusData = response['status_data'];
        final newStatus = _safeStringValue(statusData['verification_status'], 'unverified');

        // Only update if status changed
        if (newStatus != _currentVerificationStatus) {
          print('VerificationScreen: Status changed from $_currentVerificationStatus to $newStatus');

          setState(() {
            _currentVerificationStatus = newStatus;
            _isIdVerified = statusData['id_verified'] ?? false;
            _isBadgeAcquired = statusData['badge_acquired'] ?? false;

            // Update image URLs
            _uploadedFrontImageUrl = statusData['id_photo_front_url'];
            _uploadedBackImageUrl = statusData['id_photo_back_url'];
            _uploadedBrgyImageUrl = statusData['brgy_clearance_photo_url'];
          });

          // Show status change notification
          _showSnackBar('Verification status updated: ${_getStatusDisplayText(newStatus)}');
        }
      }
    } catch (e) {
      print('VerificationScreen: Error refreshing status: $e');
    }
  }

  // NEW: Show verification complete dialog
  void _showVerificationCompleteDialog() {
    if (!mounted) return;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(Icons.check_circle, color: Colors.green, size: 28),
              const SizedBox(width: 12),
              const Text('Verification Complete!'),
            ],
          ),
          content: const Text(
            'Congratulations! Your identity verification has been approved. You can now access all features of the app.',
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close dialog
                // Optionally refresh the entire screen or navigate somewhere
              },
              child: const Text('Great!'),
            ),
          ],
        );
      },
    );
  }

  // NEW: Get display text for status
  String _getStatusDisplayText(String status) {
    switch (status) {
      case 'verified':
        return 'Verified ✓';
      case 'pending_id_review':
        return 'Under Review';
      case 'pending_face_match':
        return 'Ready for Face Verification';
      case 'rejected':
        return 'Rejected';
      default:
        return 'Unverified';
    }
  }

  // NEW: Pull-to-refresh handler
  Future<void> _onRefresh() async {
    print('VerificationScreen: Pull-to-refresh triggered');

    try {
      // Stop current polling to avoid conflicts
      _stopStatusPolling();

      // Reload verification status
      await _loadUserAndVerificationStatus();

      // Show feedback to user
      // _showSnackBar('Verification status refreshed');

      print('VerificationScreen: Pull-to-refresh completed successfully');
    } catch (e) {
      print('VerificationScreen: Pull-to-refresh error: $e');
      _showSnackBar('Failed to refresh status. Please try again.', isError: true);
    }
  }

  void _showSnackBar(String message, {bool isError = false}) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              isError ? Icons.error_outline : Icons.check_circle_outline,
              color: Colors.white,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                message,
                style: const TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
        backgroundColor: isError ? Colors.red : Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        margin: const EdgeInsets.all(10),
      ),
    );
  }

  // Function to launch the live photo capture screen for ID
  Future<void> _captureIdPhoto({required bool isFront}) async {
    // Check if type of identification is selected first
    if (_selectedIdType == null) {
      _showSnackBar('Please select type of identification first.', isError: true);
      return;
    }

    final XFile? capturedImage = await Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const LivePhotoCaptureScreen()), // Reusing LivePhotoCaptureScreen for ID
    );

    if (capturedImage != null) {
      final fileSize = await capturedImage.length();
      if (fileSize > 1 * 1024 * 1024) {
        _showSnackBar('Image size exceeds 1MB. Please choose a smaller image.', isError: true);
        return;
      }
      setState(() {
        if (isFront) {
          _idPhotoFront = capturedImage;
          // Clear the uploaded URL so user sees their new local image
          _uploadedFrontImageUrl = null;
        } else {
          _idPhotoBack = capturedImage;
          // Clear the uploaded URL so user sees their new local image
          _uploadedBackImageUrl = null;
        }
      });
    }
  }
  Future<void> _capturePhoto({required Function(XFile) onCaptured}) async {
    final XFile? capturedImage = await Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const LivePhotoCaptureScreen()),
    );

    if (capturedImage != null) {
      final fileSize = await capturedImage.length();
      if (fileSize > 1 * 1024 * 1024) { // 1 MB limit
        _showSnackBar('Image size exceeds 1MB. Please choose a smaller image.', isError: true);
        return;
      }
      onCaptured(capturedImage);
    }
  }

  // NEW: Function to capture Barangay Clearance photo
  Future<void> _captureBrgyClearancePhoto() async {
    // Check if type of identification is selected first
    if (_selectedIdType == null) {
      _showSnackBar('Please select type of identification first.', isError: true);
      return;
    }

    await _capturePhoto(onCaptured: (image) {
      setState(() {
        _brgyClearancePhoto = image;
        // Clear the uploaded URL so user sees their new local image
        _uploadedBrgyImageUrl = null;
      });
    });
  }

  // Helper function to check if all verification requirements are completed
  bool _areAllRequirementsCompleted() {
    return _selectedIdType != null &&
        _idPhotoFront != null &&
        _idPhotoBack != null &&
        _brgyClearancePhoto != null &&
        _confirmIdBelongsToUser == true;
  }

  // Helper function to build image container that shows either local file or network image
  Widget _buildImageContainer({
    required String label,
    required String maxSizeText,
    required XFile? localImage,
    required String? networkImageUrl,
    required VoidCallback? onTap,
    required bool isDisabled,
    bool isBrgyUpload = false, // Special handling for barangay clearance upload
  }) {
    // Determine what to show
    bool hasLocalImage = localImage != null;
    bool hasNetworkImage = networkImageUrl != null && networkImageUrl.isNotEmpty;
    bool hasAnyImage = hasLocalImage || hasNetworkImage;

    return GestureDetector(
      onTap: isDisabled ? null : onTap,
      child: Container(
        height: 150,
        decoration: BoxDecoration(
          color: Colors.grey.shade200,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey.shade400, width: 1),
        ),
        child: hasAnyImage
            ? ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: Stack(
            children: [
              // Show the image (local file takes priority over network image)
              if (hasLocalImage)
                Image.file(
                  File(localImage!.path),
                  fit: BoxFit.cover,
                  width: double.infinity,
                  height: double.infinity,
                )
              else if (hasNetworkImage)
                Image.network(
                  '${ApiConfig.baseUrl}/../$networkImageUrl',
                  fit: BoxFit.cover,
                  width: double.infinity,
                  height: double.infinity,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      color: Colors.grey.shade300,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.error, size: 40, color: Colors.grey.shade600),
                          const SizedBox(height: 8),
                          Text(
                            'Image Error',
                            style: TextStyle(fontSize: 12, color: Colors.grey.shade700),
                          ),
                        ],
                      ),
                    );
                  },
                  loadingBuilder: (context, child, loadingProgress) {
                    if (loadingProgress == null) return child;
                    return Container(
                      color: Colors.grey.shade200,
                      child: Center(
                        child: CircularProgressIndicator(
                          value: loadingProgress.expectedTotalBytes != null
                              ? loadingProgress.cumulativeBytesLoaded / loadingProgress.expectedTotalBytes!
                              : null,
                        ),
                      ),
                    );
                  },
                ),
              // Show overlay for verification status
              if (hasNetworkImage && !hasLocalImage)
                Positioned(
                  top: 8,
                  right: 8,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: _currentVerificationStatus == 'verified'
                          ? Colors.green.withOpacity(0.9)
                          : _currentVerificationStatus == 'rejected'
                          ? Colors.red.withOpacity(0.9)
                          : Colors.orange.withOpacity(0.9),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      _currentVerificationStatus == 'verified'
                          ? 'Verified'
                          : _currentVerificationStatus == 'rejected'
                          ? 'Rejected'
                          : 'Pending',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        )
            : Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
                isBrgyUpload ? Icons.upload_file : Icons.camera_alt,
                size: isBrgyUpload ? 50 : 40,
                color: Colors.grey.shade600
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: TextStyle(
                  fontSize: isBrgyUpload ? 16 : 14,
                  color: Colors.grey.shade700
              ),
            ),
            Text(
              maxSizeText,
              style: TextStyle(
                  fontSize: isBrgyUpload ? 12 : 10,
                  color: Colors.grey
              ),
            ),
          ],
        ),
      ),
    );
  }

  // NEW: Method to trigger wiggle animation for Get Badge button
  void _triggerWiggleAnimation() {
    setState(() {
      _isWiggleAnimating = true;
    });

    // Stop animation after 1 second
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        setState(() {
          _isWiggleAnimating = false;
        });
      }
    });
  }

  // NEW: Method to trigger wiggle animation for dialog button
  void _triggerDialogButtonWiggle(StateSetter? setDialogState) {
    setState(() {
      _isDialogButtonWiggling = true;
    });

    // Also update dialog state if callback is provided
    setDialogState?.call(() {
      // This will trigger a rebuild of the dialog
    });

    // Stop animation after 1 second
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        setState(() {
          _isDialogButtonWiggling = false;
        });
        // Also update dialog state if callback is provided
        setDialogState?.call(() {
          // This will trigger a rebuild of the dialog
        });
      }
    });
  }

  // FIXED: Method to show the "Want to have Verified Badge?" prompt
  Future<void> _showVerifiedBadgePrompt() async {
    if (_currentUser == null || !_isIdVerified || _isBadgeAcquired) {
      return;
    }

    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setDialogState) {
            // Create animation controller for the button wiggle
            late AnimationController _buttonAnimationController;
            late Animation<double> _buttonWiggleAnimation;

            // Initialize animation controller
            _buttonAnimationController = AnimationController(
              vsync: Navigator.of(context),
              duration: const Duration(milliseconds: 200), // Faster wiggle
            );

            _buttonWiggleAnimation = Tween<double>(begin: -0.05, end: 0.05).animate(
              CurvedAnimation(
                parent: _buttonAnimationController,
                curve: Curves.easeInOut,
              ),
            );

            // Start animation after dialog appears
            Future.delayed(const Duration(milliseconds: 500), () {
              _buttonAnimationController.repeat(reverse: true);
            });

            return AlertDialog(
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
              title: const Text('Want to have Verified Badge?', textAlign: TextAlign.center),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Icon(Icons.check_circle, color: Constants.primaryColor, size: 20),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'In both ASAP and public listings, the platform will show you first to the lister.',
                          style: TextStyle(fontSize: 14, color: Colors.grey.shade700),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 10),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Icon(Icons.check_circle, color: Constants.primaryColor, size: 20),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'Increase your chances of being chosen - listers can see your verified badge and feel safer choosing you.',
                          style: TextStyle(fontSize: 14, color: Colors.grey.shade700),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  SizedBox(
                    width: double.infinity,
                    child: AnimatedBuilder(
                      animation: _buttonWiggleAnimation,
                      builder: (context, child) {
                        return Transform.translate(
                          offset: Offset(_buttonWiggleAnimation.value * 10.0, 0), // 10 pixels left-right wiggle
                          child: ElevatedButton(
                            onPressed: () {
                              _buttonAnimationController.stop();
                              Navigator.of(dialogContext).pop(); // Close this dialog
                              _stopPeriodicWiggleAnimation(); // Stop periodic animation
                              // Navigate to the payment screen
                              Navigator.of(context).push(
                                MaterialPageRoute(builder: (context) => const VerifiedBadgePaymentScreen()),
                              ).then((_) async {
                                // After returning from payment screen, reload user status
                                await _loadUserAndVerificationStatus();
                              });
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Constants.primaryColor,
                              foregroundColor: Colors.white,
                              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
                              padding: const EdgeInsets.symmetric(vertical: 12),
                            ),
                            child: const Column(
                              children: [
                                Text('Yes! I\'d like to have', style: TextStyle(fontSize: 16)),
                                Text('only for P99/mo', style: TextStyle(fontSize: 12)),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                  const SizedBox(height: 10),
                  SizedBox(
                    width: double.infinity,
                    child: TextButton(
                      onPressed: () {
                        _buttonAnimationController.stop();
                        Navigator.of(dialogContext).pop(); // Just close the modal
                        _stopPeriodicWiggleAnimation(); // Stop periodic animation
                        // No additional dialogs or navigation - just close the modal
                      },
                      style: TextButton.styleFrom(
                        foregroundColor: Colors.grey.shade700,
                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                      child: const Text(
                        'No. I want to be recommended last',
                        style: TextStyle(fontSize: 14),
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  // Method to show the final "Details sent!" dialog
  Future<void> _showFinalDetailsSentDialog() async {
    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
          title: const Text('Details sent!', textAlign: TextAlign.center),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'Our team will review your info first, then we\'ll notify you. Verification usually takes 1-3 days.',
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 20),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    Navigator.of(dialogContext).pop(); // Close dialog
                    Navigator.of(context).pop(); // Go back to profile settings or dashboard
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Constants.primaryColor,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                  child: const Text('OK!', style: TextStyle(fontSize: 18)),
                ),
              ),
            ],
          ),
        );
      },
    );
  }


  // Function to handle the "Next" button submission (for ID photos)
  Future<void> _submitIdPhotos() async {
    if (_currentUser == null || _currentUser!.id == null) {
      _showSnackBar('User not logged in. Cannot submit verification.', isError: true);
      return;
    }

    // Check ALL requirements first - if ANY is missing, show error and return immediately
    // Requirement 1: Type of identification selected
    if (_selectedIdType == null) {
      _showSnackBar('Please select an ID Type.', isError: true);
      return;
    }

    // Requirement 2: Front ID photo uploaded
    if (_idPhotoFront == null) {
      _showSnackBar('Please capture front ID photo.', isError: true);
      return;
    }

    // Requirement 3: Back ID photo uploaded
    if (_idPhotoBack == null) {
      _showSnackBar('Please capture back ID photo.', isError: true);
      return;
    }

    // Requirement 4: Brgy clearance uploaded
    if (_brgyClearancePhoto == null) {
      _showSnackBar('Please upload your Barangay Clearance photo.', isError: true);
      return;
    }

    // Requirement 5: ID ownership confirmed
    if (_confirmIdBelongsToUser == null || _confirmIdBelongsToUser != true) {
      _showSnackBar('Please confirm ID ownership.', isError: true);
      return;
    }

    // Check verification status before proceeding
    if (_currentVerificationStatus == 'pending') {
      print('VerificationScreen: Verification status is pending, user should not be able to submit again');
      _showSnackBar('Your verification is already pending review.', isError: false);
      return;
    }

    if (_currentVerificationStatus == 'approved' || _currentVerificationStatus == 'verified') {
      print('VerificationScreen: Verification status is approved/verified, user should not be able to submit again');
      _showSnackBar('Your verification has already been approved.', isError: false);
      return;
    }

    // Allow resubmission for rejected status
    if (_currentVerificationStatus == 'rejected') {
      print('VerificationScreen: Verification was rejected, allowing resubmission');
    }

    // Also check if user has already completed face verification, but allow resubmission for rejected status
    if (_currentUser?.livePhotoUrl != null &&
        _currentUser!.livePhotoUrl!.isNotEmpty &&
        _currentVerificationStatus != 'rejected') {
      print('VerificationScreen: User has already completed face verification, skipping modal');
      _showSnackBar('You have already completed face verification.', isError: false);
      return;
    }

    // Show loading state
    setState(() {
      _isLoading = true;
    });

    try {
      // Save ID verification data to SharedPreferences instead of uploading
      await VerificationStorage.saveIdVerificationData(
        idType: _selectedIdType!,
        frontPhotoPath: _idPhotoFront!.path,
        backPhotoPath: _idPhotoBack!.path,
        brgyPhotoPath: _brgyClearancePhoto!.path,
        confirmation: _confirmIdBelongsToUser!,
        userId: _currentUser!.id!,
      );

      // Hide loading state
      setState(() {
        _isLoading = false;
      });

      // Show success message
      _showSnackBar('ID photos saved successfully! Proceeding to face verification.');

      // Small delay for user to see the message
      await Future.delayed(const Duration(milliseconds: 1000));

      // Then proceed directly to face verification
      await _navigateToFaceVerificationDirect();
    } catch (e) {
      // Hide loading state on error
      setState(() {
        _isLoading = false;
      });

      print('VerificationScreen: Error saving verification data: $e');
      _showSnackBar('Error saving verification data: $e', isError: true);
    }
  }

  // Silently upload ID photos without showing snackbars
  Future<void> _uploadIdPhotosOnly() async {
    try {
      print('VerificationScreen: Silently uploading ID photos...');
      print('VerificationScreen: User ID: ${_currentUser!.id}');
      print('VerificationScreen: ID Type: $_selectedIdType');
      print('VerificationScreen: Front photo path: ${_idPhotoFront!.path}');
      print('VerificationScreen: Back photo path: ${_idPhotoBack!.path}');
      print('VerificationScreen: Brgy clearance path: ${_brgyClearancePhoto!.path}');
      print('VerificationScreen: Confirmation: $_confirmIdBelongsToUser');

      // Check if files exist
      print('VerificationScreen: Front photo exists: ${await File(_idPhotoFront!.path).exists()}');
      print('VerificationScreen: Back photo exists: ${await File(_idPhotoBack!.path).exists()}');
      print('VerificationScreen: Brgy clearance exists: ${await File(_brgyClearancePhoto!.path).exists()}');

      final response = await _verificationService.submitIdVerification(
        userId: _currentUser!.id!,
        idType: _selectedIdType!,
        idPhotoFrontPath: _idPhotoFront!.path,
        idPhotoBackPath: _idPhotoBack!.path,
        brgyClearancePhotoPath: _brgyClearancePhoto!.path,
        confirmation: _confirmIdBelongsToUser!,
      );

      print('VerificationScreen: ID upload response: $response');

      if (response['success']) {
        print('VerificationScreen: ID photos uploaded successfully (silent)');

        // Reload user data to get updated photo URLs
        await _loadUserAndVerificationStatus();
      } else {
        print('VerificationScreen: ID photos upload failed: ${response['message']}');
        // Show error since this is critical
        _showSnackBar('Failed to upload ID photos: ${response['message']}', isError: true);
      }
    } catch (e) {
      print('VerificationScreen: Error uploading ID photos: $e');
      print('VerificationScreen: Stack trace: ${StackTrace.current}');
      // Show error since this is critical
      _showSnackBar('Error uploading ID photos: $e', isError: true);
    }
  }

  // Navigate directly to face verification without modal
  Future<void> _navigateToFaceVerificationDirect() async {
    print('VerificationScreen: Navigating directly to face verification...');

    if (mounted) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => const EnhancedFaceVerificationScreen(
            shouldSubmitIdAfterFace: true, // Changed: Will submit all data after face capture
          ),
        ),
      ).then((_) {
        // After face verification is completed, reload verification status
        _loadUserAndVerificationStatus();
      });
    }
  }

  // Navigate to face verification and pass ID verification data
  Future<void> _navigateToFaceVerification() async {
    print('VerificationScreen: Navigating to face verification with ID data for later submission');

    // Prepare ID verification data to pass to face verification screen
    final idVerificationData = {
      'idType': _selectedIdType!,
      'idPhotoFrontPath': _idPhotoFront!.path,
      'idPhotoBackPath': _idPhotoBack!.path,
      'brgyClearancePhotoPath': _brgyClearancePhoto!.path,
      'confirmation': _confirmIdBelongsToUser!,
    };

    if (mounted) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => EnhancedFaceVerificationScreen(
            shouldSubmitIdAfterFace: true,
            idVerificationData: idVerificationData,
          ),
        ),
      ).then((_) {
        // After face verification is completed, reload verification status
        _loadUserAndVerificationStatus();
      });
    }
  }

  // Separate function to handle the actual ID submission (called after face verification)
  Future<void> _performIdSubmission() async {
    setState(() {
      _isLoading = true;
    });

    final response = await _verificationService.submitIdVerification(
      userId: _currentUser!.id!,
      idType: _selectedIdType!,
      idPhotoFrontPath: _idPhotoFront!.path,
      idPhotoBackPath: _idPhotoBack!.path,
      brgyClearancePhotoPath: _brgyClearancePhoto!.path,
      confirmation: _confirmIdBelongsToUser!,
    );

    setState(() {
      _isLoading = false;
    });

    if (response['success']) {
      _showSnackBar(response['message'] ?? 'ID photos submitted successfully!');

      // Update status to pending and start polling
      setState(() {
        _currentVerificationStatus = 'pending_id_review';
      });
      _startStatusPolling();

      // Navigate to face verification after successful submission
      if (mounted) {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => const EnhancedFaceVerificationScreen(
              shouldSubmitIdAfterFace: false, // ID already submitted
            ),
          ),
        ).then((_) => _loadUserAndVerificationStatus());
      }
    } else {
      _showSnackBar('ID submission failed: ${response['message']}', isError: true);
    }
  }

  // NEW: Show verified badge modal before submission
  Future<String?> _showVerifiedBadgeModal() async {
    final result = await showDialog<String>(
      context: context,
      barrierDismissible: true, // Allow dismissing by tapping outside
      builder: (BuildContext context) {
        return _buildVerifiedBadgeModal();
      },
    );

    if (result == 'yes') {
      // User wants verified badge - navigate to payment first
      if (mounted) {
        Navigator.of(context).push(
          MaterialPageRoute(builder: (context) => const VerifiedBadgePaymentScreen()),
        ).then((_) => _loadUserAndVerificationStatus());
      }
    }
    // If result is 'no', just return it - the calling function will handle it

    return result;
  }

  // NEW: Method to start periodic wiggle animation
  void _startPeriodicWiggleAnimation() {
    _wiggleTimer?.cancel(); // Cancel any existing timer
    _wiggleTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      if (mounted && _isIdVerified && !_isBadgeAcquired) {
        setState(() {
          _isWiggleAnimating = true;
        });

        // Stop animation after 1 second
        Future.delayed(const Duration(seconds: 1), () {
          if (mounted) {
            setState(() {
              _isWiggleAnimating = false;
            });
          }
        });
      } else {
        timer.cancel(); // Stop timer if conditions are no longer met
      }
    });
  }

  // NEW: Method to stop periodic wiggle animation
  void _stopPeriodicWiggleAnimation() {
    _wiggleTimer?.cancel();
    _wiggleTimer = null;
  }

  // NEW: Method to start periodic dialog button wiggle animation
  void _startDialogButtonWiggleAnimation(StateSetter? setDialogState) {
    _dialogButtonTimer?.cancel(); // Cancel any existing timer
    _dialogButtonTimer = Timer.periodic(const Duration(seconds: 3), (timer) {
      if (mounted) {
        setState(() {
          _isDialogButtonWiggling = true;
        });

        // Stop animation after 1 second
        Future.delayed(const Duration(seconds: 1), () {
          if (mounted) {
            setState(() {
              _isDialogButtonWiggling = false;
            });
          }
        });
      } else {
        timer.cancel(); // Stop timer if widget is disposed
      }
    });
  }

  // NEW: Method to stop periodic dialog button wiggle animation
  void _stopDialogButtonWiggleAnimation() {
    _dialogButtonTimer?.cancel();
    _dialogButtonTimer = null;
  }

  // NEW: Build verified badge modal widget
  Widget _buildVerifiedBadgeModal() {
    return StatefulBuilder(
      builder: (BuildContext context, StateSetter setModalState) {
        return AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
          contentPadding: const EdgeInsets.all(32),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Blue checkmark icon
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: const Color(0xFF4285F4), // Google Blue
                  borderRadius: BorderRadius.circular(40),
                ),
                child: const Icon(
                  Icons.check,
                  color: Colors.white,
                  size: 40,
                ),
              ),
              const SizedBox(height: 24),

              // Title
              const Text(
                'Get Your Verified Badge',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),

              // Benefits list
              Column(
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        margin: const EdgeInsets.only(top: 2),
                        child: const Icon(
                          Icons.check,
                          color: Colors.green,
                          size: 20,
                        ),
                      ),
                      const SizedBox(width: 12),
                      const Expanded(
                        child: Text(
                          'Be featured first — Appear at the top of listings in both ASAP and public search results.',
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        margin: const EdgeInsets.only(top: 2),
                        child: const Icon(
                          Icons.check,
                          color: Colors.green,
                          size: 20,
                        ),
                      ),
                      const SizedBox(width: 12),
                      const Expanded(
                        child: Text(
                          'Boost your credibility — Verified users are more likely to be chosen, giving listers greater confidence and peace of mind.',
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 32),

              // Yes button with wiggle animation
              SizedBox(
                width: double.infinity,
                child: AnimatedWiggleButton(
                  onPressed: () {
                    Navigator.of(context).pop('yes');
                  },
                  isAnimating: true, // Always animate when modal is shown
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF4285F4), // Same blue as icon
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                  ),
                  child: const Text(
                    'Yes! I\'d like to get verified',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 8),

              // Price text
              const Text(
                '(only for P99/month)',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),

              // No button
              GestureDetector(
                onTap: () {
                  Navigator.of(context).pop('no');
                },
                child: const Text(
                  'No thanks, continue with face verification',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    String statusMessage;
    Color statusColor;
    IconData statusIcon;

    switch (_currentVerificationStatus) {
      case 'verified':
        statusMessage = 'You are verified!';
        statusColor = Colors.green.shade700;
        statusIcon = Icons.check_circle;
        break;
      case 'pending_id_review': // Now covers ID + Brgy Clearance review
        statusMessage = 'Documents submitted. Awaiting review.';
        statusColor = Colors.orange.shade700;
        statusIcon = Icons.pending_actions;
        break;
      case 'pending_face_match':
        statusMessage = 'ID photos reviewed. Proceed to face verification.';
        statusColor = Colors.blue.shade700;
        statusIcon = Icons.face;
        break;
      case 'rejected':
        statusMessage = 'Verification rejected. Please try again.';
        statusColor = Colors.red.shade700;
        statusIcon = Icons.cancel;
        break;
      default: // 'unverified' or unknown
        statusMessage = 'Please submit your ID for verification.';
        statusColor = Colors.grey.shade700;
        statusIcon = Icons.info_outline;
        break;
    }
    // Disable form when verification is pending, verified, or any status other than unverified/rejected
    final bool disableDocumentUpload = (_currentVerificationStatus == 'pending' ||
        _currentVerificationStatus == 'verified' ||
        _currentVerificationStatus == 'approved' ||
        _currentVerificationStatus == 'pending_id_review' ||
        _currentVerificationStatus == 'pending_face_match');

    // Allow resubmission for rejected status
    final bool allowResubmission = (_currentVerificationStatus == 'rejected' ||
        _currentVerificationStatus == 'unverified');

    return Scaffold(
      appBar: AppBar(
        title: const Text('Verification'),
        backgroundColor: Constants.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
        onRefresh: _onRefresh,
        color: Constants.primaryColor,
        backgroundColor: Colors.white,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(), // Enables pull-to-refresh even when content doesn't fill screen
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Status Display (Badge-like UI)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: statusColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: statusColor),
                ),
                child: Row(
                  children: [
                    Icon(statusIcon, color: statusColor, size: 30),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            statusMessage,
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: statusColor,
                            ),
                          ),
                          if (_isIdVerified && _currentVerificationStatus == 'verified' && _isBadgeAcquired)
                            Text(
                              'Verified Badge Acquired!',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.green.shade900,
                                fontWeight: FontWeight.bold,
                              ),
                            ),

                        ],
                      ),
                    ),
                    if (_isIdVerified && !_isBadgeAcquired && _currentVerificationStatus == 'verified')
                      AnimatedWiggleButton(
                        onPressed: () {
                          _triggerWiggleAnimation();
                          _showVerifiedBadgePrompt();
                        },
                        isAnimating: _isWiggleAnimating,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Constants.primaryColor,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                        ),
                        child: const Text('Get Badge'),
                      ),
                  ],
                ),
              ),
              const SizedBox(height: 24),

              const Text(
                'Type of Identification',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: Constants.textColor),
              ),
              const SizedBox(height: 8),
              // Horizontal scrollable ID Type buttons (replace Dropdown)
              SizedBox(
                height: 50, // Fixed height for the row of buttons
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: _idTypes.length,
                  itemBuilder: (context, index) {
                    final idType = _idTypes[index];
                    final isSelected = _selectedIdType == idType;
                    return Padding(
                      padding: const EdgeInsets.only(right: 8.0),
                      child: ElevatedButton(
                        onPressed: disableDocumentUpload ? null : () { // Disable if pending/verified/approved
                          setState(() {
                            _selectedIdType = idType;
                          });
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: isSelected ? Constants.primaryColor : Colors.grey.shade200,
                          foregroundColor: isSelected ? Colors.white : Constants.textColor,
                          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                          side: BorderSide(color: isSelected ? Constants.primaryColor : Colors.grey.shade400),
                          elevation: isSelected ? 4 : 1,
                          minimumSize: const Size(120, 50), // Minimum size for buttons
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                        ),
                        child: Text(idType),
                      ),
                    );
                  },
                ),
              ),
              const SizedBox(height: 24),

              const Text(
                'Upload your ID photos*',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: Constants.textColor),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: _buildImageContainer(
                      label: 'ID Front',
                      maxSizeText: '1 MB max',
                      localImage: _idPhotoFront,
                      networkImageUrl: _uploadedFrontImageUrl,
                      onTap: () => _captureIdPhoto(isFront: true),
                      isDisabled: disableDocumentUpload,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildImageContainer(
                      label: 'ID Back',
                      maxSizeText: '1 MB max',
                      localImage: _idPhotoBack,
                      networkImageUrl: _uploadedBackImageUrl,
                      onTap: () => _captureIdPhoto(isFront: false),
                      isDisabled: disableDocumentUpload,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),
              // NEW: Upload Brgy. Clearance Section
              const Text(
                'Upload Brgy. Clearance*',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: Constants.textColor),
              ),
              const SizedBox(height: 8),
              Container(
                width: double.infinity,
                child: _buildImageContainer(
                  label: 'Click here to upload',
                  maxSizeText: 'Photo size: 1 MB max',
                  localImage: _brgyClearancePhoto,
                  networkImageUrl: _uploadedBrgyImageUrl,
                  onTap: _captureBrgyClearancePhoto,
                  isDisabled: disableDocumentUpload,
                  isBrgyUpload: true,
                ),
              ),
              const SizedBox(height: 24),

              const Text(
                'Do you confirm this ID belongs to you?*',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: Constants.textColor),
              ),
              Row(
                children: [
                  Expanded(
                    child: RadioListTile<bool>(
                      title: const Text('Yes'),
                      value: true,
                      groupValue: _confirmIdBelongsToUser,
                      onChanged: disableDocumentUpload ? null : (bool? value) {
                        setState(() {
                          _confirmIdBelongsToUser = value;
                        });
                      },
                    ),
                  ),
                  Expanded(
                    child: RadioListTile<bool>(
                      title: const Text('No'),
                      value: false,
                      groupValue: _confirmIdBelongsToUser,
                      onChanged: disableDocumentUpload ? null : (bool? value) {
                        setState(() {
                          _confirmIdBelongsToUser = value;
                        });
                      },
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 32),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: (_isLoading || disableDocumentUpload)
                      ? null // Disable if loading or when document upload is disabled (pending/verified status)
                      : _submitIdPhotos, // Calls the submission for ID photos
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Constants.primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                  ),
                  child: _isLoading
                      ? const CircularProgressIndicator(color: Colors.white)
                      : Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Show pending icon for pending states
                      if (_currentVerificationStatus == 'pending' ||
                          _currentVerificationStatus == 'pending_id_review' ||
                          _currentVerificationStatus == 'approved') ...[
                        const Icon(Icons.pending_actions, color: Colors.white, size: 20),
                        const SizedBox(width: 8),
                      ],
                      // Show verified icon for verified state
                      if (_currentVerificationStatus == 'verified') ...[
                        const Icon(Icons.check_circle, color: Colors.white, size: 20),
                        const SizedBox(width: 8),
                      ],
                      // Show face icon for face verification state
                      if (_currentVerificationStatus == 'pending_face_match') ...[
                        const Icon(Icons.face, color: Colors.white, size: 20),
                        const SizedBox(width: 8),
                      ],
                      Flexible(
                        child: Text(
                          _getButtonText(),
                          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Returns the appropriate button text based on current verification status
  String _getButtonText() {
    switch (_currentVerificationStatus) {
      case 'verified':
        return 'VERIFIED';
      case 'pending':
        return 'Verification Pending Review';
      case 'pending_id_review':
        return 'Documents Pending Review';
      case 'pending_face_match':
        return 'Proceed to Face Verification';
      case 'approved':
        return 'Verification Approved';
      case 'rejected':
        return 'Resubmit Verification';
      default: // 'unverified' or unknown
        return 'Next (Submit ID Photos)';
    }
  }

  /// Clean up expired verification data from SharedPreferences
  Future<void> _cleanupExpiredData() async {
    try {
      await VerificationStorage.clearExpiredData();
      print('VerificationScreen: Cleanup completed');
    } catch (e) {
      print('VerificationScreen: Error during cleanup: $e');
    }
  }

  /// Check if there's pending verification data in SharedPreferences
  Future<void> _checkPendingVerificationData() async {
    try {
      final hasData = await VerificationStorage.hasIdVerificationData();
      if (hasData) {
        final isExpired = await VerificationStorage.isVerificationDataExpired();
        if (isExpired) {
          await VerificationStorage.clearVerificationData();
          print('VerificationScreen: Cleared expired verification data');
        } else {
          print('VerificationScreen: Found pending verification data in storage');
          // Optionally show a message to user about pending data
          // _showSnackBar('You have pending verification data. Please complete face verification.', isError: false);
        }
      }
    } catch (e) {
      print('VerificationScreen: Error checking pending data: $e');
    }
  }
}
