import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:camera/camera.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import 'dart:async';
import 'dart:io';
import 'dart:math' as math;
import 'dart:typed_data';
import 'package:hanapp/utils/constants.dart' as Constants;
import 'package:hanapp/services/verification_service.dart';
import 'package:hanapp/utils/auth_service.dart';
import 'package:hanapp/models/user.dart';
import 'package:hanapp/utils/verification_storage.dart';

enum FaceScanStep { centerFace, faceRight, faceLeft, keepSteady, countdown, completed }

class EnhancedFaceVerificationScreen extends StatefulWidget {
  final bool shouldSubmitIdAfterFace;
  final Map<String, dynamic>? idVerificationData;

  const EnhancedFaceVerificationScreen({
    super.key,
    this.shouldSubmitIdAfterFace = false,
    this.idVerificationData,
  });

  @override
  State<EnhancedFaceVerificationScreen> createState() => _EnhancedFaceVerificationScreenState();
}

class _EnhancedFaceVerificationScreenState extends State<EnhancedFaceVerificationScreen>
    with TickerProviderStateMixin {
  CameraController? _cameraController;
  late FaceDetector _faceDetector;
  bool _isDetecting = false;
  bool _isCameraInitialized = false;
  bool _isLoading = false;

  // Face scan progress
  double _scanProgress = 0.0;
  bool _faceDetected = false;
  bool _scanCompleted = false;

  // New face scan rules state
  FaceScanStep _currentStep = FaceScanStep.centerFace;
  String _instructionText = "Center your face in the circle";

  // Step completion tracking
  bool _centerCompleted = false;
  bool _rightCompleted = false;
  bool _leftCompleted = false;

  // Timing for steady and countdown
  DateTime? _steadyStartTime;
  DateTime? _countdownStartTime;
  int _countdownSeconds = 2;

  Timer? _progressTimer;
  Timer? _steadyTimer;
  Timer? _countdownTimer;

  // Animation controllers
  late AnimationController _pulseController;
  late AnimationController _progressController;
  late Animation<double> _pulseAnimation;

  // Services
  final VerificationService _verificationService = VerificationService();
  User? _currentUser;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeFaceDetector();
    _initializeCamera();
    _loadCurrentUser();
  }

  void _initializeAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _progressController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _pulseController.repeat(reverse: true);
  }

  void _initializeFaceDetector() {
    _faceDetector = FaceDetector(
      options: FaceDetectorOptions(
        enableContours: false,
        enableLandmarks: false,
        enableClassification: false,
        enableTracking: false,
        minFaceSize: 0.05, // Lower threshold for better detection
        performanceMode: FaceDetectorMode.fast, // Use fast mode for better real-time performance
      ),
    );
  }

  Future<void> _initializeCamera() async {
    try {
      final cameras = await availableCameras();
      if (cameras.isEmpty) {
        _showSnackBar('No cameras available', isError: true);
        return;
      }

      // Use front camera for selfie
      final frontCamera = cameras.firstWhere(
            (camera) => camera.lensDirection == CameraLensDirection.front,
        orElse: () => cameras.first,
      );

      _cameraController = CameraController(
        frontCamera,
        ResolutionPreset.medium,
        enableAudio: false,
        imageFormatGroup: ImageFormatGroup.nv21,
      );

      await _cameraController!.initialize();

      if (mounted) {
        setState(() {
          _isCameraInitialized = true;
        });

        // Start image stream after a short delay to ensure camera is ready
        Future.delayed(const Duration(milliseconds: 500), () {
          if (mounted && _cameraController != null && _cameraController!.value.isInitialized) {
            _startImageStream();
          }
        });
      }
    } catch (e) {
      _showSnackBar('Failed to initialize camera: $e', isError: true);
    }
  }

  void _startImageStream() {
    if (_cameraController == null || !_cameraController!.value.isInitialized) return;

    try {
      _cameraController!.startImageStream((CameraImage image) {
        if (!_isDetecting && !_scanCompleted && mounted) {
          _isDetecting = true;
          _detectFaces(image);
        }
      });
    } catch (e) {
      // Error starting image stream
    }
  }

  Future<void> _detectFaces(CameraImage image) async {
    try {
      final inputImage = _inputImageFromCameraImage(image);
      if (inputImage == null) {
        _isDetecting = false;
        return;
      }

      final faces = await _faceDetector.processImage(inputImage);

      if (mounted) {
        setState(() {
          _faceDetected = faces.isNotEmpty;
        });

        if (faces.isNotEmpty) {
          final face = faces.first;
          _processFaceMovement(face);
        } else {
          _resetProgress();
        }
      }
    } catch (e) {
      // Error detecting faces
    } finally {
      _isDetecting = false;
    }
  }

  void _processFaceMovement(Face face) {
    if (_scanCompleted) return;

    final rotY = face.headEulerAngleY ?? 0.0;
    final rotZ = face.headEulerAngleZ ?? 0.0;

    // Check face position and orientation based on current step
    switch (_currentStep) {
      case FaceScanStep.centerFace:
        _processCenterFace(rotY, rotZ);
        break;
      case FaceScanStep.faceRight:
        _processFaceRight(rotY);
        break;
      case FaceScanStep.faceLeft:
        _processFaceLeft(rotY);
        break;
      case FaceScanStep.keepSteady:
        _processKeepSteady(rotY, rotZ);
        break;
      case FaceScanStep.countdown:
        _processCountdown();
        break;
      case FaceScanStep.completed:
        break;
    }
  }

  void _processCenterFace(double rotY, double rotZ) {
    // Check if face is centered (minimal rotation)
    bool isCentered = rotY.abs() < 15.0 && rotZ.abs() < 15.0;

    if (isCentered && !_centerCompleted) {
      setState(() {
        _centerCompleted = true;
        _currentStep = FaceScanStep.faceRight;
        _instructionText = "Turn your face to the right";
        _scanProgress = 0.33;
      });
    }
  }

  void _processFaceRight(double rotY) {
    // Check if face is turned right (negative Y rotation due to mirrored camera)
    bool isFacingRight = rotY < -25.0;

    if (isFacingRight && !_rightCompleted) {
      setState(() {
        _rightCompleted = true;
        _currentStep = FaceScanStep.faceLeft;
        _instructionText = "Turn your face to the left";
        _scanProgress = 0.66;
      });
    }
  }

  void _processFaceLeft(double rotY) {
    // Check if face is turned left (positive Y rotation due to mirrored camera)
    bool isFacingLeft = rotY > 25.0;

    if (isFacingLeft && !_leftCompleted) {
      setState(() {
        _leftCompleted = true;
        _currentStep = FaceScanStep.keepSteady;
        _instructionText = "Keep steady for 5 seconds";
        _scanProgress = 0.8;
        _steadyStartTime = DateTime.now();
      });
      _startSteadyTimer();
    }
  }

  void _processKeepSteady(double rotY, double rotZ) {
    // Check if face is steady (minimal movement)
    bool isSteady = rotY.abs() < 15.0 && rotZ.abs() < 15.0;

    if (!isSteady) {
      // Reset steady timer if face moves too much
      _steadyTimer?.cancel();
      setState(() {
        _steadyStartTime = DateTime.now();
      });
      _startSteadyTimer();
    }
  }

  void _processCountdown() {
    // Countdown is handled by timer, just update progress
    if (_countdownStartTime != null) {
      final elapsed = DateTime.now().difference(_countdownStartTime!).inMilliseconds;
      final progress = 0.8 + (0.2 * (elapsed / 2000.0)); // 0.8 to 1.0 over 2 seconds
      setState(() {
        _scanProgress = math.min(1.0, progress);
      });
    }
  }

  void _startSteadyTimer() {
    _steadyTimer = Timer(const Duration(seconds: 5), () {
      if (_currentStep == FaceScanStep.keepSteady) {
        setState(() {
          _currentStep = FaceScanStep.countdown;
          _countdownSeconds = 2;
          _countdownStartTime = DateTime.now();
          _instructionText = "Hold still... $_countdownSeconds";
        });
        _startCountdownTimer();
      }
    });
  }

  void _startCountdownTimer() {
    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _countdownSeconds--;
        if (_countdownSeconds > 0) {
          _instructionText = "Hold still... $_countdownSeconds";
        } else {
          _instructionText = "Capturing...";
        }
      });

      if (_countdownSeconds <= 0) {
        timer.cancel();
        _completeScan();
      }
    });
  }

  void _completeScan() {
    setState(() {
      _scanCompleted = true;
      _currentStep = FaceScanStep.completed;
      _scanProgress = 1.0;
    });

    _progressController.forward();
    _pulseController.stop();

    // Capture the photo after a short delay
    Timer(const Duration(milliseconds: 500), () {
      _capturePhoto();
    });
  }

  void _resetProgress() {
    // Reset to previous step if face is not detected
    _steadyTimer?.cancel();
    _countdownTimer?.cancel();

    if (_currentStep == FaceScanStep.keepSteady || _currentStep == FaceScanStep.countdown) {
      // Go back to center face if in steady or countdown phase
      setState(() {
        _currentStep = FaceScanStep.centerFace;
        _instructionText = "Center your face in the circle";
        _scanProgress = 0.0;
        _centerCompleted = false;
        _rightCompleted = false;
        _leftCompleted = false;
      });
    }
  }

  Future<void> _capturePhoto() async {
    if (_cameraController == null || !_cameraController!.value.isInitialized) {
      return;
    }

    try {
      setState(() {
        _isLoading = true;
      });

      final XFile photo = await _cameraController!.takePicture();
      await _submitVerification(photo.path);

    } catch (e) {
      _showSnackBar('Failed to capture photo: $e', isError: true);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  InputImage? _inputImageFromCameraImage(CameraImage image) {
    try {
      final camera = _cameraController!.description;

      // Get rotation based on device orientation and camera orientation
      InputImageRotation rotation;
      switch (camera.sensorOrientation) {
        case 90:
          rotation = InputImageRotation.rotation90deg;
          break;
        case 180:
          rotation = InputImageRotation.rotation180deg;
          break;
        case 270:
          rotation = InputImageRotation.rotation270deg;
          break;
        default:
          rotation = InputImageRotation.rotation0deg;
      }

      // Handle different image formats more carefully
      InputImageFormat? format;
      switch (image.format.raw) {
        case 35: // ImageFormat.YUV_420_888 on Android
          format = InputImageFormat.nv21; // Use NV21 instead of YUV420
          break;
        case 17: // ImageFormat.NV21 on Android
          format = InputImageFormat.nv21;
          break;
        case 875704438: // kCVPixelFormatType_420YpCbCr8BiPlanarVideoRange on iOS
          format = InputImageFormat.bgra8888;
          break;
        default:
          return null; // Unsupported image format
      }

      // For YUV_420_888, we need to handle the planes differently
      Uint8List bytes;
      int bytesPerRow;

      if (image.format.raw == 35) { // YUV_420_888
        // Convert YUV_420_888 to NV21 format
        final yPlane = image.planes[0];
        final uPlane = image.planes[1];
        final vPlane = image.planes[2];

        final ySize = yPlane.bytes.length;
        final uvSize = uPlane.bytes.length;

        bytes = Uint8List(ySize + uvSize);
        bytes.setRange(0, ySize, yPlane.bytes);

        // Interleave U and V bytes for NV21 format
        int uvIndex = ySize;
        for (int i = 0; i < uvSize; i += uPlane.bytesPerPixel!) {
          if (uvIndex < bytes.length - 1) {
            bytes[uvIndex] = vPlane.bytes[i];
            bytes[uvIndex + 1] = uPlane.bytes[i];
            uvIndex += 2;
          }
        }

        bytesPerRow = yPlane.bytesPerRow;
      } else {
        final plane = image.planes.first;
        bytes = plane.bytes;
        bytesPerRow = plane.bytesPerRow;
      }

      return InputImage.fromBytes(
        bytes: bytes,
        metadata: InputImageMetadata(
          size: Size(image.width.toDouble(), image.height.toDouble()),
          rotation: rotation,
          format: format,
          bytesPerRow: bytesPerRow,
        ),
      );
    } catch (e) {
      return null;
    }
  }

  Future<void> _loadCurrentUser() async {
    final user = await AuthService.getUser();
    setState(() {
      _currentUser = user;
    });
  }

  Future<void> _submitVerification(String photoPath) async {
    if (_currentUser == null) {
      _showSnackBar('User not found. Please log in again.', isError: true);
      return;
    }

    try {
      setState(() {
        _isLoading = true;
      });

      if (widget.shouldSubmitIdAfterFace) {
        final response = await _verificationService.submitCompleteVerificationFromStorage(
          facePhotoPath: photoPath,
        );

        if (response['success']) {
          _showVerificationCompleteDialog();
        } else {
          _showSnackBar('Verification failed: ${response['message']}', isError: true);
        }
      } else {
        final response = await _verificationService.requestFaceVerification(
          userId: _currentUser!.id!,
          livePhotoPath: photoPath,
        );

        if (response['success']) {
          _showVerificationCompleteDialog();
        } else {
          _showSnackBar('Face verification failed: ${response['message']}', isError: true);
        }
      }
    } catch (e) {
      _showSnackBar('Failed to submit verification: $e', isError: true);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showSnackBar(String message, {bool isError = false}) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _showVerificationCompleteDialog() {
    if (!mounted) return;

    showDialog(
      context: context,
      barrierDismissible: false, // User must tap OK to dismiss
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const SizedBox(height: 16),
                const Text(
                  'Details sent! Our team will review your info first, then we\'ll notify you. Verification usually takes 1-3 days',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.black87,
                    height: 1.4,
                  ),
                ),
                const SizedBox(height: 32),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pop(); // Close dialog
                      if (mounted) {
                        Navigator.of(context).pop(); // Go back to verification screen
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF4A47FF),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text(
                      'OK!',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        title: const Text('Face Verification'),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Stack(
        children: [
          // Camera preview
          if (_isCameraInitialized && _cameraController != null)
            Positioned.fill(
              child: FittedBox(
                fit: BoxFit.cover,
                child: SizedBox(
                  width: _cameraController!.value.previewSize!.height,
                  height: _cameraController!.value.previewSize!.width,
                  child: CameraPreview(_cameraController!),
                ),
              ),
            )
          else
            const Center(
              child: CircularProgressIndicator(color: Colors.white),
            ),

          // Face scan overlay
          Positioned.fill(
            child: CustomPaint(
              painter: FaceScanOverlayPainter(
                progress: _scanProgress,
                faceDetected: _faceDetected,
                scanCompleted: _scanCompleted,
                pulseAnimation: _pulseAnimation,
              ),
            ),
          ),





          // Instructions
          Positioned(
            bottom: 120,
            left: 0,
            right: 0,
            child: Column(
              children: [
                AnimatedBuilder(
                  animation: _pulseAnimation,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _faceDetected ? _pulseAnimation.value : 1.0,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                        margin: const EdgeInsets.symmetric(horizontal: 32),
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.7),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          _getInstructionText(),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    );
                  },
                ),
                const SizedBox(height: 16),
                // Progress indicator
                Container(
                  width: 200,
                  height: 8,
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: FractionallySizedBox(
                    alignment: Alignment.centerLeft,
                    widthFactor: _scanProgress,
                    child: Container(
                      decoration: BoxDecoration(
                        color: _scanCompleted ? Colors.green : Constants.primaryColor,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Loading overlay
          if (_isLoading)
            Container(
              color: Colors.black.withOpacity(0.7),
              child: const Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CircularProgressIndicator(color: Colors.white),
                    SizedBox(height: 16),
                    Text(
                      'Processing verification...',
                      style: TextStyle(color: Colors.white, fontSize: 16),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  String _getInstructionText() {
    if (_scanCompleted) {
      return 'Scan completed! Processing...';
    } else if (_faceDetected) {
      return _instructionText;
    } else {
      return 'Position your face in the center and look at the camera.';
    }
  }

  @override
  void dispose() {
    _cameraController?.dispose();
    _faceDetector.close();
    _pulseController.dispose();
    _progressController.dispose();
    _progressTimer?.cancel();
    _steadyTimer?.cancel();
    _countdownTimer?.cancel();
    super.dispose();
  }
}

class FaceScanOverlayPainter extends CustomPainter {
  final double progress;
  final bool faceDetected;
  final bool scanCompleted;
  final Animation<double> pulseAnimation;

  FaceScanOverlayPainter({
    required this.progress,
    required this.faceDetected,
    required this.scanCompleted,
    required this.pulseAnimation,
  }) : super(repaint: pulseAnimation);

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2 - 50);
    final radius = 150.0;

    // Draw dark overlay with circular cutout
    final overlayPaint = Paint()
      ..color = Colors.black.withOpacity(0.7)
      ..style = PaintingStyle.fill;

    final overlayPath = Path()
      ..addRect(Rect.fromLTWH(0, 0, size.width, size.height))
      ..addOval(Rect.fromCircle(center: center, radius: radius + 10))
      ..fillType = PathFillType.evenOdd;

    canvas.drawPath(overlayPath, overlayPaint);

    // Draw segmented progress indicators
    _drawSegmentedProgress(canvas, center, radius);
  }

  void _drawSegmentedProgress(Canvas canvas, Offset center, double radius) {
    const int totalSegments = 60; // Total number of segments
    const double segmentAngle = 2 * math.pi / totalSegments;
    const double segmentLength = 8.0;
    const double segmentWidth = 3.0;
    const double gapSize = 2.0;

    final completedSegments = (progress * totalSegments).floor();

    for (int i = 0; i < totalSegments; i++) {
      final angle = i * segmentAngle - math.pi / 2; // Start from top
      final isCompleted = i < completedSegments;

      // Determine segment color
      Color segmentColor;
      if (scanCompleted) {
        segmentColor = Colors.green;
      } else if (isCompleted) {
        segmentColor = Constants.primaryColor;
      } else {
        segmentColor = Colors.white.withOpacity(0.3);
      }

      final paint = Paint()
        ..color = segmentColor
        ..style = PaintingStyle.stroke
        ..strokeWidth = segmentWidth
        ..strokeCap = StrokeCap.round;

      // Calculate segment positions
      final startRadius = radius - segmentLength / 2;
      final endRadius = radius + segmentLength / 2;

      final startX = center.dx + startRadius * math.cos(angle);
      final startY = center.dy + startRadius * math.sin(angle);
      final endX = center.dx + endRadius * math.cos(angle);
      final endY = center.dy + endRadius * math.sin(angle);

      canvas.drawLine(
        Offset(startX, startY),
        Offset(endX, endY),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(FaceScanOverlayPainter oldDelegate) {
    return oldDelegate.progress != progress ||
        oldDelegate.faceDetected != faceDetected ||
        oldDelegate.scanCompleted != scanCompleted;
  }
}
